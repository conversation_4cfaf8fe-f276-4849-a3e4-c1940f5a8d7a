import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  Platform,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  Modal,
  TextInput
} from 'react-native';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {stringsTo} from '../../../../globalization/Localize';
import {showToast, MessageDialog, imiThemeManager} from '../../../../imilab-design-ui';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {LetDevice} from '../../../../imilab-rn-sdk';
import DatePicker from '../../../../imilab-design-ui/src/widgets/settingUI/DatePicker';

const {width: screenWidth, height: screenHeight} = Dimensions.get("screen");

export default class FamilyProtectionTimeSetting extends React.Component {
  
  static navigationOptions = () => {
    return {
      headerTransparent: true,
      header: null
    };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      showNameDialog: false,
      showRepeatModeDialog: false,
      showRepeatWeekDialog: false,
      showTimeDialog: false,
      showSaveDialog: false,
      canSave: false,
      setTime: 0, // 0 开始时间，1 结束时间
      tempRepeatCopy: 0,
      prePositionNameTooLong: false,
      commentErr: "",
      // 名称输入相关状态
      nameInputValue: "", // 当前输入的名称
      nameInputError: "" // 名称输入错误信息
    };
    this.data = {
      selectedIndexArray: [-1],
      multiIndexArray: []
    };
    
    this.item = this.props.route.params?.item;
    this.existList = this.props.route.params?.existList || [];
    this.pageCallback = this.props.route.params?.callback;
    
    if (this.item) {
      this.setWeekByRepeat();
    } else {
      this.item = {repeat: 0};
    }
    
    // 初始化 tempRepeatCopy
    this.state.tempRepeatCopy = this.item.repeat || 0;
    console.log("item==", this.item);
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    if (this.state.canSave) {
      this.setState({showSaveDialog: true});
      return true;
    }
    return false;
  };

  // 确保只显示一个dialog的方法
  showSingleDialog = (dialogName, additionalState = {}) => {
    const newState = {
      showNameDialog: false,
      showTimeDialog: false,
      showRepeatModeDialog: false,
      showRepeatWeekDialog: false,
      showSaveDialog: false,
      ...additionalState
    };

    newState[dialogName] = true;
    this.setState(newState);
  };

  // 名称输入处理方法
  onNameInputChange = (text) => {
    this.setState({
      nameInputValue: text,
      nameInputError: ""
    });
  };

  // 打开名称输入对话框
  openNameDialog = () => {
    this.showSingleDialog('showNameDialog', {
      nameInputValue: this.item.name || "",
      nameInputError: ""
    });
  };

  // 关闭名称输入对话框
  closeNameDialog = () => {
    this.setState({
      showNameDialog: false,
      nameInputValue: "",
      nameInputError: ""
    });
  };

  // 确认名称输入
  confirmNameInput = () => {
    const trimmedName = this.state.nameInputValue.trim();

    // 验证名称不能为空
    if (!trimmedName) {
      this.setState({nameInputError: "名称不能为空"});
      return;
    }

    // 验证名称不能重复（排除当前编辑的项目）
    const existingItem = this.existList.find(item =>
      item.name === trimmedName && item !== this.item
    );
    if (existingItem) {
      this.setState({nameInputError: "名称已存在"});
      return;
    }

    // 设置名称并关闭对话框
    this.item.name = trimmedName;
    this.setState({
      showNameDialog: false,
      nameInputValue: "",
      nameInputError: "",
      canSave: true
    });
  };

  setWeekByRepeat() {
    let flag = 0b00000001;
    let i = 0;
    this.data.multiIndexArray = [];
    for (i = 0; i < 7; i++) {
      if ((this.item.repeat & flag) != 0) {
        this.data.multiIndexArray.push((i + 6) % 7);
      }
      flag = flag << 1;
    }
    if (0b00000000 == this.item.repeat) {
      this.data.selectedIndexArray = [0];
    } else if (0b01111111 == this.item.repeat) {
      this.data.selectedIndexArray = [1];
    } else {
      this.data.selectedIndexArray = [2];
    }
  }

  checkAndSubmit() {
    if (!this.item.name || this.item.name.trim() == "") {
      showToast("名称不能为空");
      return;
    }

    if ((!this.item.start || this.item.start.trim() == "") && (!this.item.end || this.item.end.trim() == "")) {
      showToast("请设置监控时间");
      return;
    }
    if (!this.item.start || this.item.start.trim() == "") {
      showToast("请设置开始时间");
      return;
    }

    if (!this.item.end || this.item.end.trim() == "") {
      showToast("请设置结束时间");
      return;
    }

    if (this.item.start == this.item.end) {
      showToast("开始时间和结束时间不能相同");
      return;
    }

    if (this.isSameNameSet()) {
      showToast("名称已存在");
      return;
    }

    if (this.isSameSet()) {
      showToast("监控时段已存在");
      return;
    }



    this.item.enable = true;
    this.pageCallback && this.pageCallback(this.item);
    this.props.navigation.goBack();

    /* TODO: 后期替换为真实接口调用，类似这样：
    showLoading("保存中...", true);

    const params = {
      FamilyCareTimePeriod: [{
        name: this.item.name,
        start: this.item.start,
        end: this.item.end,
        repeat: this.item.repeat,
        enable: true
      }]
    };

    LetDevice.setPropertyCloud(JSON.stringify(params)).then((res) => {
      showLoading(false);
      showToast("设置成功");
      this.item.enable = true;
      this.pageCallback && this.pageCallback(this.item);
      this.props.navigation.goBack();
    }).catch((err) => {
      showLoading(false);
      showToast("设置失败");
      console.log('保存失败:', err);
    });
    */
  }

  isSameNameSet() {
    let index = this.existList.findIndex((item) => {
      return item.name == this.item.name;
    });
    return index > -1;
  }

  isSameSet() {
    let index = this.existList.findIndex((item) => {
      return item.start == this.item.start && item.end == this.item.end && item.repeat == this.item.repeat;
    });
    return index > -1;
  }

  getRepeatString(repeat) {
    if (repeat === 0) {
      return stringsTo('plug_timer_onetime');
    } else if (repeat === 0b01111111) {
      return stringsTo('plug_timer_everyday');
    } else {
      const weekDays = [
        stringsTo('sunday1'),
        stringsTo('monday1'),
        stringsTo('tuesday1'),
        stringsTo('wednesday1'),
        stringsTo('thursday1'),
        stringsTo('friday1'),
        stringsTo('saturday1')
      ];
      let selectedDays = [];
      let flag = 0b00000001;
      for (let i = 0; i < 7; i++) {
        if ((repeat & flag) !== 0) {
          selectedDays.push(weekDays[i]);
        }
        flag = flag << 1;
      }
      return selectedDays.join(' ');
    }
  }

  getEndTimeValue() {
    if (!this.item.start) {
      return this.item.end ? this.item.end : "未设置";
    }

    if (this.item.end) {
      let startValue = parseInt(this.item.start.split(":")[0]) * 60 + parseInt(this.item.start.split(":")[1]);
      let endValue = parseInt(this.item.end.split(":")[0]) * 60 + parseInt(this.item.end.split(":")[1]);
      let text = this.item.end;
      if (startValue > endValue) {
        text = `${stringsTo('setting_monitor_next_day')} ${this.item.end}`;
      }
      return text;
    }

    return "未设置";
  }

  renderNameDialog() {
    const maxLength = 20;
    const currentLength = this.state.nameInputValue.length;
    const hasError = !!this.state.nameInputError;
    const isValid = this.state.nameInputValue.trim().length > 0 && !hasError;

    return (
      <MessageDialog
        visible={this.state.showNameDialog}
        title="名称"
        onDismiss={this.closeNameDialog}
        buttons={[
          {
            text: "取消",
            callback: this.closeNameDialog
          },
          {
            text: "确定",
            callback: this.confirmNameInput,
            disabled: !isValid
          }
        ]}
      >
        <View style={styles.nameDialogContent}>
          <View style={styles.nameInputContainer}>
            <TextInput
              ref={(ref) => this.nameInputRef = ref}
              style={[
                styles.nameTextInput,
                hasError && styles.nameTextInputError
              ]}
              placeholder="名称"
              placeholderTextColor="#999"
              value={this.state.nameInputValue}
              onChangeText={this.onNameInputChange}
              maxLength={maxLength}
              autoFocus={true}
              clearButtonMode="while-editing"
              returnKeyType="done"
              onSubmitEditing={this.confirmNameInput}
            />
            <Text style={[
              styles.nameCharCount,
              currentLength >= maxLength && styles.nameCharCountLimit
            ]}>
              {currentLength}/{maxLength}
            </Text>
          </View>
          {hasError && (
            <Text style={styles.nameErrorText}>
              {this.state.nameInputError}
            </Text>
          )}
        </View>
      </MessageDialog>
    );
  }

  renderRepeatWeekDialog() {
    return (
      <Modal
        animationType="none"
        transparent={true}
        visible={this.state.showRepeatWeekDialog}
        onRequestClose={() => {
          this.setState({ showRepeatWeekDialog: false });
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.repeatViewStyle}>
            {this._renderWeekSelection()}
          </View>
        </View>
      </Modal>
    );
  }

  _renderWeekSelection() {
    const weekOptions = [
      {title: stringsTo('monday1'), index: 0},
      {title: stringsTo('tuesday1'), index: 1},
      {title: stringsTo('wednesday1'), index: 2},
      {title: stringsTo('thursday1'), index: 3},
      {title: stringsTo('friday1'), index: 4},
      {title: stringsTo('saturday1'), index: 5},
      {title: stringsTo('sunday1'), index: 6}
    ];

    return (
      <View style={{ alignItems: "center", marginBottom: 16 }}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {stringsTo('custom_repeat')}
        </Text>
        <View style={{ marginTop: 15 }}>
          {weekOptions.map((option, index) => {
            const isSelected = this.data.multiIndexArray.includes(index);
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  const newArray = [...this.data.multiIndexArray];
                  if (isSelected) {
                    // 取消选择
                    const idx = newArray.indexOf(index);
                    if (idx > -1) {
                      newArray.splice(idx, 1);
                    }
                  } else {
                    // 添加选择
                    newArray.push(index);
                  }
                  this.data.multiIndexArray = newArray;
                  this.forceUpdate(); // 强制更新以显示选择状态
                }}
              >
                <View
                  style={{
                    maxWidth: "100%",
                    width: screenWidth,
                    height: 54,
                    backgroundColor: "#ffffff",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between"
                  }}
                >
                  <Text
                    style={{
                      marginLeft: 30,
                      fontSize: 16,
                      color: isSelected ? "#32BAC0" : "#000000",
                      fontWeight: "500"
                    }}
                  >
                    {option.title}
                  </Text>
                  {isSelected && (
                    <Image
                      style={{ width: 22, height: 22, marginRight: 22 }}
                      source={require("../../resources/images/icon_select_s.png")}
                    />
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{
          flexDirection: "row",
          justifyContent: "space-between",
          marginTop: 20,
          marginLeft: 0,
          marginBottom: 10
        }}>
          <TouchableOpacity
            onPress={() => {
              this.setState({ showRepeatWeekDialog: false });
            }}
          >
            <View style={{
              width: 147,
              height: 46,
              backgroundColor: "#F5F5F5",
              borderRadius: 23,
              justifyContent: "center",
              alignItems: "center"
            }}>
              <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{stringsTo('cancel')}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              let repeat = 0b00000000;
              this.data.multiIndexArray.forEach((item) => {
                repeat = repeat | (0b00000001 << (item + 1) % 7);
              });
              this.setState({
                showRepeatWeekDialog: false,
                tempRepeatCopy: repeat
              });
            }}
          >
            <View style={{
              width: 147,
              height: 46,
              backgroundColor: "#32BAC0",
              borderRadius: 23,
              justifyContent: "center",
              alignItems: "center",
              marginLeft: 20
            }}>
              <Text style={{ fontSize: 16, color: "#ffffff" }}>{stringsTo('ok_button')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  renderTimeDialog() {
    // 获取当前系统时间
    const now = new Date();
    const currentHour = String(now.getHours()).padStart(2, '0');
    const currentMinute = String(now.getMinutes()).padStart(2, '0');

    // 如果已有设置的时间，使用设置的时间；否则使用当前系统时间
    let defaultTime;
    if (this.state.setTime == 0) {
      // 开始时间：如果已设置则使用已设置的，否则使用当前时间
      defaultTime = this.item.start ? this.item.start : `${currentHour}:${currentMinute}`;
    } else {
      // 结束时间：如果已设置则使用已设置的，否则使用当前时间
      defaultTime = this.item.end ? this.item.end : `${currentHour}:${currentMinute}`;
    }

    // 解析时间为数组格式 ['08', '00']
    const timeParts = defaultTime.split(':');
    const currentSelectTime = [
      timeParts[0] || currentHour,
      timeParts[1] || currentMinute
    ];
    
    return (
      <DatePicker
        visible={this.state.showTimeDialog}
        type="time24"
        title={this.state.setTime == 0 ? "开始时间" : "结束时间"}
        currentSelectTime={currentSelectTime}
        onDismiss={() => this.setState({showTimeDialog: false})}
        onSelectConfirm={(time) => {
          console.log('时间选择器返回值:', time.rawArray, time.date);

          // 验证数据格式并处理
          if (time.rawArray && time.rawArray.length >= 2) {
            let hour = time.rawArray[0];
            let minute = time.rawArray[1];

            // 格式化为两位数
            hour = String(hour).padStart(2, '0');
            minute = String(minute).padStart(2, '0');

            const timeStr = hour + ':' + minute;
            console.log('处理后的时间字符串:', timeStr);

            if (this.state.setTime == 0) {
              this.item.start = timeStr;
            } else {
              this.item.end = timeStr;
            }
            this.setState({canSave: true, showTimeDialog: false});
          } else {
            console.error('时间选择器返回数据格式异常:', time);
            this.setState({showTimeDialog: false});
          }
        }}
      />
    );
  }

  renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        title="要舍弃对该设置的改动吗？"
        buttons={[
          {
            text: "取消",
            callback: () => this.setState({showSaveDialog: false})
          },
          {
            text: "舍弃更改",
            callback: () => {
              this.setState({showSaveDialog: false});
              this.props.navigation.goBack();
            }
          }
        ]}
        onDismiss={() => this.setState({showSaveDialog: false})}
      >
        <View />
      </MessageDialog>
    );
  }

  renderRepeatViewDialog() {
    return (
      <Modal
        animationType="none"
        transparent={true}
        visible={this.state.showRepeatModeDialog}
        onRequestClose={() => {
          this.setState({ showRepeatModeDialog: false });
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.repeatViewStyle}>
            {this._renderRepeatView()}
          </View>
        </View>
      </Modal>
    );
  }

  _renderRepeatView() {
    console.log("++++++++++++", this.state.tempRepeatCopy);
    this.repeatItems = [
      {
        title: stringsTo('plug_timer_onetime'),
        select: 0b00000000 == this.state.tempRepeatCopy
      },
      {
        title: stringsTo('plug_timer_everyday'),
        select: 0b01111111 == this.state.tempRepeatCopy
      },
      {
        title: stringsTo('custom_repeat'),
        select: 0b00000000 != this.state.tempRepeatCopy && 0b01111111 != this.state.tempRepeatCopy
      }
    ];
    return (
      <View style={{ alignItems: "center", marginBottom: 16 }}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {stringsTo("plug_timer_repeat")}
        </Text>
        <View style={{ marginTop: 15 }}>
          {this.repeatItems.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  if (index == 0) {
                    // 执行一次
                    this.setState({ tempRepeatCopy: 0b00000000 });
                  } else if (index == 1) {
                    // 每天
                    this.setState({ tempRepeatCopy: 0b01111111 });
                  } else {
                    // 自定义
                    let flag = 0b00000001;
                    let i = 0;
                    this.data.multiIndexArray = [];
                    for (i = 0; i < 7; i++) {
                      if ((this.state.tempRepeatCopy & flag) != 0) {
                        this.data.multiIndexArray.push((i + 6) % 7);
                      }
                      flag = flag << 1;
                    }
                    this.setState({ showRepeatWeekDialog: true });
                  }
                }}
              >
                <View
                  style={{
                    maxWidth: "100%",
                    width: screenWidth,
                    height: 54,
                    backgroundColor:
                                          item.select == true ? "rgba(50,186,192,0.1)" : "#ffffff",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between"
                  }}
                  key={index}
                >
                  <Text
                    style={{
                      marginLeft: 30,
                      fontSize: 16,
                      color: item.select == true ? "#32BAC0" : "#000000",
                      fontWeight: "500"
                    }}
                  >
                    {item.title}
                  </Text>
                  {item.select == true && (
                    <Image
                      style={{ width: 22, height: 22, marginRight: 22 }}
                      source={require("../../resources/images/icon_select_s.png")}
                    ></Image>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{
          flexDirection: "row",
          justifyContent: "space-between",
          marginTop: 20,
          marginLeft: 0,
          marginBottom: 10
        }}>
          <TouchableOpacity
            onPress={() => {
              this.setState({ showRepeatModeDialog: false });
            }}
          >
            <View style={{
              width: 147,
              height: 46,
              backgroundColor: "#F5F5F5",
              borderRadius: 23,
              justifyContent: "center",
              alignItems: "center"
            }}>
              <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{stringsTo('cancel')}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              let canSave = this.state.canSave || this.item.repeat != this.state.tempRepeatCopy;
              this.item.repeat = this.state.tempRepeatCopy;
              this.setState({ showRepeatModeDialog: false, canSave: canSave });
            }}
          >
            <View style={{
              width: 147,
              height: 46,
              backgroundColor: "#32BAC0",
              borderRadius: 23,
              justifyContent: "center",
              alignItems: "center",
              marginLeft: 20
            }}>
              <Text style={{ fontSize: 16, color: "#ffffff" }}>{stringsTo('ok_button')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title="时间段设置"
          left={[
            {
              key: NavigationBar.ICON.CUSTOM,
              n_source: require('../../resources/images/houseKeepingV2/icon_angel_del.png'),
              onPress: () => {
                if (this.state.canSave) {
                  this.setState({showSaveDialog: true});
                  return;
                }
                this.props.navigation.goBack();
              },
              accessibilityLabel: 'family_protection_close'
            }
          ]}
          right={[
            {
              key: NavigationBar.ICON.CUSTOM,
              n_source: require('../../resources/images/houseKeepingV2/icon_angel_save.png'),
              onPress: () => {
                this.checkAndSubmit();
              },
              accessibilityLabel: 'family_protection_save'
            }
          ]}
        />

        <ScrollView style={styles.scrollView}>
          <ListItem
            title="名称"
            value={this.item.name ? this.item.name : "未设置"}
            onPress={this.openNameDialog}
          />

          <ListItem
            title="开始时间"
            value={this.item.start ? this.item.start : "未设置"}
            onPress={() => {
              this.showSingleDialog('showTimeDialog', {
                setTime: 0
              });
            }}
          />

          <ListItem
            title="结束时间"
            value={this.getEndTimeValue()}
            onPress={() => {
              this.showSingleDialog('showTimeDialog', {
                setTime: 1
              });
            }}
          />

          <ListItem
            title="重复"
            value={this.item ? this.getRepeatString(this.item.repeat) : stringsTo('plug_timer_onetime')}
            onPress={() => {
              this.showSingleDialog('showRepeatModeDialog', {
                tempRepeatCopy: this.item ? (this.item.repeat || 0) : 0
              });
            }}
          />
        </ScrollView>

        {this.renderNameDialog()}
        {this.renderRepeatWeekDialog()}
        {this.renderTimeDialog()}
        {this.renderBackDialog()}
        {this.renderRepeatViewDialog()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF'
  },
  scrollView: {
    flex: 1
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end'
  },
  repeatViewStyle: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginHorizontal: 0
  },
  // 名称输入dialog样式
  nameDialogContent: {
    paddingVertical: 10,
    paddingHorizontal: 14
  },
  nameInputContainer: {
    marginBottom: 10
  },
  nameTextInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#f9f9f9',
    minHeight: 44
  },
  nameTextInputError: {
    borderColor: '#ff4444',
    backgroundColor: '#fff5f5'
  },
  nameCharCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4
  },
  nameCharCountLimit: {
    color: '#ff4444'
  },
  nameErrorText: {
    fontSize: 12,
    color: '#ff4444',
    marginTop: 4,
    textAlign: 'center'
  }
});
